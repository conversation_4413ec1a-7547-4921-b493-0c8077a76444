import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_BASE_URL;


export const fetchStores = async (concept, territory) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/scenario/get-store/`, {
      params: {
        concept: concept,
        territory: territory
      }
    })
    console.log("response value ", response.data)
    return response.data
  } catch (error) {
    console.error('Failed to fetch store list:', error)
    return []
  }
}

export const saveScenarioAPI = async (data) => {
  try {

    const response = await axios.post(`${API_BASE_URL}/scenario/create-scenario/`, 
      data,
       {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }
    );
    sessionStorage.setItem('scenario_id',response.data.scenario_details.scenario_id)
    return response.data;
  } catch (error) {
    throw error.response;
  }
};


export const getClustersAPI = async (payload) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/scenario/clusters/`, payload)
    
    return response.data
  } catch (err) {
    throw err.response || err
  }
}

export const updateStoreClustersAPI = async (payload) => {
  try {
    const response = await axios.put(`${API_BASE_URL}/scenario/clusters/`, payload);
    return response.data;
  } catch (error) {
    throw error.response || error;
  }
};

export const deleteStoreFromClusterAPI = async ({ loc_cd, cluster_num, concept, scenario_id }) => {
  try {
    const response = await axios.delete(`${API_BASE_URL}/scenario/clusters/`, {
      data: { loc_cd, cluster_num, concept, scenario_id }
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

export const getPreOptAPI = async (payload) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/scenario/preopt/`, payload)
    
    return response.data
  } catch (err) {
    throw err.response || err
  }
}

export const fetchRefPeriod = async (concept, territory) => {
  try {
    console.log("concept 1111111 ", concept, territory)
    const response = await axios.get(`${API_BASE_URL}/scenario/getRefPeriod/`, {
      params: {
        concept: concept,
        territory: territory
      }
    })
    console.log("response value ", response.data)
    return response.data
  } catch (error) {
    console.error('Failed to fetch store list:', error)
    return []
  }
}

export const updateScenarioStatus = async (payload) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/scenario/updateScenarioStatus/`, payload)
    
    return response.data
  } catch (err) {
    throw err.response || err
  }
}
export const setOptimizerRunStatus = async (payload) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/scenario/updateRunStatus/`, payload)
    
    return response.data
  } catch (err) {
    throw err.response || err
  }
}
export const getOptimizerRunStatus = async (scenario_id, value) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/scenario/updateRunStatus/`, {
      params: {
        scenario_id,
        value
      }
    })
    console.log("response value ", )
    return response.data[value]
  } catch (err) {
    throw err.response || err
  }
}

export const dynamicFilters = async (scenario_id,concept, territory, page) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/scenario/getFilterData/`
      , {
        concept,
        scenario_id,
        territory,
        page
      
      })
    return response.data
  } catch (err) {
    throw err.response || err
  }
}

export async function fetchOutlierVisualizationData(params) {
  try {
    const response = await axios.get('scenario/getOutlierVisualization/', {
      params: {
        concept: sessionStorage.getItem('concept'),
        scenario_id: sessionStorage.getItem('scenario_id'),
        territory: sessionStorage.getItem('territory_name'),
        performance_metric: sessionStorage.getItem('performance_metric'),
        loc_cd: params.loc_cd && params.loc_cd.length > 0
          ? params.loc_cd
          : (JSON.parse(sessionStorage.getItem('loc_codes') || '[]'))[0] || null,
        cluster: params.cluster,
        sub_class: params.sub_class,
    
      }
    });
    return response.data;
  } catch (error) {
    throw error;
  }
}
export const fetchStoreDataAvailability = async (formData) => {
  try {
    // Get concept and territory from formData first, fallback to sessionStorage
    const concept = sessionStorage.getItem("concept") || "hb"
    const territory = formData.territory || sessionStorage.getItem("territory_name")


    if (!territory || !concept) {
      console.error("No territory or concept available for store data availability check")
      return null
    }

    const params = { concept, territory }

    if (formData.configurationStore === "Selected Stores" && formData.storeSelection && formData.storeSelection.length > 0) {
      params.store_config = "selected_stores"
      params.loc_cd = formData.storeSelection.map(s => s.value || s)
    } else if (formData.configurationStore === "Test & Control") {
      params.store_config = "test_control"
    }

    console.log("fetchStoreDataAvailability - params:", params)

    const response = await axios.get(`${API_BASE_URL}/scenario/storeDataAvailability/`, { params })
    console.log("fetchStoreDataAvailability - response:", response.data)

    return response.data

  } catch (err) {
    console.error("Error fetching store data availability:", err)
    console.error("Error details:", err.response?.data || err.message)
    return null
  }
}

export const downloadRangeBasedCsv = async (scenarioId, concept, territory) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/scenario/downloadRangeData/`, {
      params: {
        scenario_id: scenarioId,
        concept,
        territory,
      },
      responseType: 'blob', // Required to handle binary data
    });

    // ✅ Extract filename from response header
    const disposition = response.headers['content-disposition'];
    const filename = `Range_${concept}_${territory}.csv`;

    // ✅ Create blob and trigger download
    const blob = new Blob([response.data], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', filename);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url); // Clean up

  } catch (error) {
    console.error('CSV Download failed:', error);
    throw error;
  }
};

export const downloadSummaryBasedCsv = async (scenarioId, concept, territory) => {
  try {
    const params = {
      scenario_id: scenarioId,
      concept,
      territory,
    };

    const response = await axios.get(`${API_BASE_URL}/scenario/downloadRangeSummary/`, {
      params,
      responseType: 'blob',
    });

    // Extract filename
    const disposition = response.headers['content-disposition'];
    let filename = 'RangeSummary.csv';
    if (disposition && disposition.includes('filename=')) {
      const match = disposition.match(/filename="?([^"]+)"?/);
      if (match && match[1]) {
        filename = match[1];
      }
    }

    // Trigger download
    const blob = new Blob([response.data], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', filename);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Download failed:', error);
    throw error;
  }
};

export const resetClustersAPI = async ({ scenario_id, concept }) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/scenario/resetCluster/`, {
      scenario_id,
      concept
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

export const resetOptimizationToBaselineAPI = async ({ scenario_id, concept }) => {
  try {
    const response = await axios.post(`${API_BASE_URL}/scenario/resetOptimizationToBaseline/`, {
      scenario_id,
      concept
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

export const deleteScenarioAPI = async (scenario_id) => {
  try {
    const response = await axios.delete(`${API_BASE_URL}/scenario/deleteScenario/${scenario_id}/`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

