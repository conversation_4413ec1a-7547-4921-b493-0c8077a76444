<script setup lang="ts">
import { ref, computed, onMounted, toRaw, nextTick, watch } from 'vue';
import { baseFastapiURL } from '../main';
import { Download, Loader, RotateCcw } from "lucide-vue-next";
import axios from 'axios'
import DynamicFilter from './common/DynamicFilter.vue'
import ProductivityChartsDashboard from './ProductivityChartsDashboard.vue'
import { useStepsStore } from '../stores/NavigationStore.js'
import { useRouter } from 'vue-router'
import AlertPage from "./common/AlertPage.vue"
import { getOptimizerRunStatus, setOptimizerRunStatus, downloadRangeBasedCsv, updateScenarioStatus, downloadSummaryBasedCsv } from '../services/api.js';
import { useToast } from 'vue-toastification'


const router = useRouter()
const toast = useToast()
const alertMessage = ref(`Your setup is now complete.
The evaluation will run in the background.
Please return after the evaluation period to view the results.`)
const headerMessage = ref("All Steps Completed")
const showAlert = ref(false)
const aggregatedOptimizedMetric = ref(0);
const aggregatedUpliftPct = ref(0);
const props = defineProps({
  scenarioPhase: {
    type: String,
    default: ''
  }
})
const isDisabled = computed(() => props.scenarioPhase === 'completed')

const handleOk = () => {
  completeOptimization()
  saveOptimiserProgress('next')
  showAlert.value = false
}


const handleCancel = () => {
  showAlert.value = false
}

// Track which cell is being edited: { [rowKey_colKey]: true }
const editingCells = ref({})

function getCellKey(row, colKey) {
  return row.LOC_CD + '-' + row.SUB_CLSS_NM + '-' + colKey
}

function startEdit(row, colKey) {
  editingCells.value[getCellKey(row, colKey)] = true
}

function saveEdit(row, colKey) {
  editingCells.value[getCellKey(row, colKey)] = false
  recordChange(row, colKey, row[colKey])
}

function isEditing(row, colKey) {
  return !!editingCells.value[getCellKey(row, colKey)]
}

const stepsStore = useStepsStore()
const storedData = stepsStore.getScenarioData;
let optimizationData = ref([]);
let rangeOptimizationData = ref([]);
let upliftData = ref([]);
let allStores = ref([]);
let dataCount = ref(10);
let lastScrollTop = 0
const optimizationFilterData = ref([]);
const showRangeOptimizerButtons = ref(false)

const isLoading = ref(false)
const showProductivityModal = ref(false)
const runMode = ref('Optimization')

watch(showProductivityModal, async (val) => {
  if (val) {
    await nextTick()
    setTimeout(() => {
      window.dispatchEvent(new Event('resize'))
    }, 0)
  }
})

const checkNeedForPagination = async () => {
  const el = scrollContainer.value;
  if (!el || isLoading.value || isFetchingNextPage.value) return;

  // If container is NOT scrollable but more pages exist
  const isScrollable = el.scrollHeight > el.clientHeight;
  if (!isScrollable && currentPage.value < totalPages.value) {
    isFetchingNextPage.value = true;
    currentPage.value += 1;
    if (runMode.value === 'Optimization') {
      await fetchOptimizationData(currentPage.value, rowsPerPage, true);
    } else {
      await fetchRangeData(currentPage.value, rowsPerPage, true);
    }
    isFetchingNextPage.value = false;

    // Call again in nextTick to check if it's scrollable now
    await nextTick();
    checkNeedForPagination();
  }
};

const fetchOptimizationData = async (current_page = 1, limit = 10, onscroll=false,skipLoading=false) => {
  if (!onscroll && !skipLoading) isLoading.value = true;
  try {
    const response = await axios.post('scenario/getOptimizationSummary/', {
      concept: sessionStorage.getItem('concept'),
      territory: sessionStorage.getItem('territory_name'),
      scenario_id: sessionStorage.getItem('scenario_id'),
      group: selectedGroups.value,
      department: selectedDepartments.value,
      class_field: selectedClasses.value,
      sub_class: selectedSubClasses.value,
      loc_cd: Array.isArray(selectedStores.value)
  ? selectedStores.value
  : selectedStores.value ? [selectedStores.value] : [],
      recommendation: selectedRecommendation.value,
      page: current_page,
      limit: limit
    })
    if (onscroll) {
      optimizationData.value = [...optimizationData.value, ...response.data.data];
    } else {
      optimizationData.value = response.data.data;
    }
    dataCount.value = response.data.count
    showFiltered.value = false;
    if(response.data.data.length === 0) {
      await runOptimizer('optimization')
    }
  } catch (err) {
    console.error('Error fetching optimization Data:', err)
  } finally {
    isLoading.value = false
  }
}

const fetchOptimizationFilters = async () => {
  isLoading.value = true;
  try {
    const response = await axios.get('scenario/getOptimizationFilters/', {
      params: {
        concept: sessionStorage.getItem('concept'),
        scenario_id: sessionStorage.getItem('scenario_id'),
        territory: sessionStorage.getItem('territory_name')
      }
    });

    const filters = response.data.filters.gdcs_data;
    // Set unique filter arrays as needed...
    optimizationFilterData.value = filters;
    const all_location = response.data.filters
    allStores.value = all_location.locations;
    selectedStores.value = all_location.locations[0]?.LOC_CD ? [all_location.locations[0].LOC_CD] : []

  } catch (error) {
    console.error("Error fetching optimization filters", error);
  }
  finally {
    isLoading.value = false;
  }
};

const fetchRangeData = async (current_page = 1, limit = 10, onscroll = false) => {
  if (!onscroll) isLoading.value = true;

  try {
    const response = await axios.get('/scenario/getRangeSummary/', {
      params: {
        concept: sessionStorage.getItem('concept'),
        territory: sessionStorage.getItem('territory_name'),
        scenario_id: sessionStorage.getItem('scenario_id'),
        group: selectedGroups.value,
        department: selectedDepartments.value,
        class_field: selectedClasses.value,
        sub_class: selectedSubClasses.value,
        loc_cd: Array.isArray(selectedStores.value)
          ? selectedStores.value
          : selectedStores.value
          ? [selectedStores.value]
          : [],
        recommendation: selectedRecommendation.value,
        page: current_page,
        limit: limit
      }
    });

    if (onscroll) {
      rangeOptimizationData.value = [...rangeOptimizationData.value, ...response.data.data];
    } else {
      rangeOptimizationData.value = response.data.data;
    }
    if (showRangeFiltered.value) {
      filteredRangeData.value = response.data.data;
    }

    dataCount.value = response.data.count;
    showRangeFiltered.value = false;
  } catch (err) {
    console.error('Error fetching range data:', err);
  } finally {
    isLoading.value = false;
  }
};


async function getUpliftData() {
  isLoading.value = true;
  try {
    const response = await axios.post('/scenario/getUpliftData/', {
      scenario_id: sessionStorage.getItem('scenario_id'),
      concept: sessionStorage.getItem('concept'),
    })
    upliftData.value = response.data.data
  } catch (err) {
    console.error('Error fetching data:', err)
  }
  finally {
    isLoading.value = false
  }
}

async function getRangeUpliftData() {
  isLoading.value = true;
  try {
    const response = await axios.get('/scenario/getRangeUpliftData/', {
      params: {
        scenario_id: sessionStorage.getItem('scenario_id'),
        concept: sessionStorage.getItem('concept')
      }
    })
    upliftData.value = response.data.data
  } catch (err) {
    console.error('Error fetching data:', err)
  }
  finally {
    isLoading.value = false
  }
}

const runOptimizer = async (module) => {
  isLoading.value = true
  try {
    const response = await fetch(`${baseFastapiURL}/run/${module}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        SCENARIO_ID: String(sessionStorage.getItem('scenario_id')),
      })
    });

    if (!response.ok) {
      throw new Error(`API error ${response.status}: ${response.statusText}`);
    }
    await new Promise(resolve => setTimeout(resolve, 2000));
    await fetchOptimizationData(currentPage.value, rowsPerPage)
    await getUpliftData()
    const json = await response.json();
    await setOptimizerRunStatus({
      scenario_id: sessionStorage.getItem('scenario_id'),
      run_optimizer: 1,
      run_performance: 1
    })
    return json;
  } catch (e) {
    console.error('Failed to call pre-fetch API', e);
    throw e; // rethrow so caller knows it failed
  } finally {
    isLoading.value = false
  }
};
const reRunOptimizer = async (module) => {
  isLoading.value = true
  try {
    const response = await fetch(`${baseFastapiURL}/run/${module}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        SCENARIO_ID: String(sessionStorage.getItem('scenario_id')),
        OVERRIDE_DICT: toRaw(changedRecords.value)
      })
    });

    if (!response.ok) {
      throw new Error(`API error ${response.status}: ${response.statusText}`);
    }
    await new Promise(resolve => setTimeout(resolve, 2000));
    await fetchOptimizationData(currentPage.value, rowsPerPage)
    await getUpliftData()
    changedRecords = ref({})
    await setOptimizerRunStatus({
      scenario_id: sessionStorage.getItem('scenario_id'),
      run_optimizer: 1,
      run_performance: 1
    })
    const json = await response.json();
    return json;
  } catch (e) {
    console.error('Failed to call pre-fetch API', e);
    throw e; // rethrow so caller knows it failed
  } finally {
    isLoading.value = false
  }
};

const runRange = async (module) => {
  isLoading.value = true
  try {
    const response = await fetch(`${baseFastapiURL}/run/${module}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        SCENARIO_ID: String(sessionStorage.getItem('scenario_id')),
      })
    });

    if (!response.ok) {
      throw new Error(`API error ${response.status}: ${response.statusText}`);
    }
    await setOptimizerRunStatus({
      scenario_id: sessionStorage.getItem('scenario_id'),
      run_optimizer: 1,
      run_performance: 1,
      run_range: 1,
    })
    return;
  } catch (e) {
    console.error('Failed to call Range API', e);
    throw e; // rethrow so caller knows it failed
  } finally {
    isLoading.value = false
  }
};

onMounted(() => {
  ; (async () => {
    const seasonType = storedData.season_type
    if (seasonType === 'in season') {
    showRangeOptimizerButtons.value = true
  } else {
    showRangeOptimizerButtons.value = false
  }
    let isValid = await getOptimizerRunStatus(sessionStorage.getItem('scenario_id'), 'runOptimizer')
    await fetchOptimizationFilters();
    if(!isValid) {
      await runOptimizer('optimization')
    }
    else{
    await fetchOptimizationData(currentPage.value, rowsPerPage)
    }
    await getUpliftData();
    calculateAggregatedMetrics();
    checkNeedForPagination();
  })()
})

const calcMetricChangePercent = (row: any) => {
  const val = parseFloat(row.current_per_day_metric)
  const optMetric = parseFloat(row.new_metric_per_day)

  if (!isNaN(val) && val !== 0 && !isNaN(optMetric)) {
    const percentChange = ((optMetric - val) / val) * 100
    return percentChange.toFixed(2)
  }

  return '-'
}

const calcOptionsChange = (row: any) => {
  const avg = parseFloat(row.AVG_OPTN_CNT);
  const opt = parseFloat(row.optimized_no_of_options);

  if (isNaN(avg) || isNaN(opt)) return '-';

  const diff =
    row.Final_Action === 'increase'
      ? opt - avg
      : avg - opt;

  return Math.round(diff) || 0;
};


const calcQuantityChange = (row: any) => {
  const avg = parseFloat(row.MNTH_AVG_SOH_AVG);
  const opt = parseFloat(row.optimized_qty);

  if (isNaN(avg) || isNaN(opt)) return '-';

  const diff = row.Final_Action === 'increase'
      ? opt - avg
      : avg - opt;

  return Math.round(diff) || 0;
};


// Capitalize and color-code recommendation
const capitalizeRecommendation = (val: any) => {
  if (!val) return ''
  return String(val).charAt(0).toUpperCase() + String(val).slice(1).toLowerCase()
}

const recommendationClass = (val: any) => {
  const v = String(val).toLowerCase()
  if (v === 'increase') return 'bg-green-100 text-tertiary text-[13px] font-semibold px-1 py-0.5 rounded'
  if (v === 'decrease') return 'bg-red-100 text-red-700 text-[13px] font-semibold px-1 py-0.5 rounded'
  if (v === 'no change') return 'bg-gray-100 text-gray-700 text-[13px] font-semibold px-0.5 py-0.5 rounded'
  return ''
}


const performanceMetric = sessionStorage.getItem('performance_metric')
// Define mapping for labels & keys
const metricConfig = {
  GMV: {
    baseKey: 'GMV_sum_reference_month',
    baseKeyColoumnData: 'GMV_sum_reference_month',
    baseLabel: 'GMV',
    optKey: 'GMV_sum_optimized',
    optLabel: 'Optimized GMV',
    changeLabel: 'GMV Change %',
    evalPeriodLabel: 'Total Optimized GMV',
  },
  REVENUE: {
    baseKey: 'Revenue_sum_reference_month',
    baseKeyColoumnData: 'NET_SLS_AMT_sum_reference_month',
    baseLabel: 'Revenue',
    optKey: 'Revenue_sum_optimized',
    optLabel: 'Optimized Revenue',
    changeLabel: 'Revenue Change %',
    evalPeriodLabel: 'Total Optimized Revenue',
  }
}

const metricRangeConfig = {
  GMV: {
    baseKey: 'GMV_sum_reference_month',
    baseKeyColoumnData: 'gmv_sum_reference_month',
    baseLabel: 'GMV',
    optKey: 'GMV_sum_optimized',
    optLabel: 'Optimized GMV',
    changeLabel: 'GMV Change %',
    evalPeriodLabel: 'Total Optimized GMV',
  },
  REVENUE: {
    baseKey: 'Revenue_sum_reference_month',
    baseKeyColoumnData: 'net_sls_amt_sum_reference_month',
    baseLabel: 'Revenue',
    optKey: 'Revenue_sum_optimized',
    optLabel: 'Optimized Revenue',
    changeLabel: 'Revenue Change %',
    evalPeriodLabel: 'Total Optimized Revenue',
  }
}

const metric = metricConfig[performanceMetric] || metricConfig.GMV
const rangeMetric = metricRangeConfig[performanceMetric] || metricConfig.GMV
let metricShortNm = performanceMetric === 'REVENUE' ? 'rev' : 'gmv'
const tableColumns = [
  { key: 'LOC_CD', label: 'Store' },
  { key: 'GRP_NM', label: 'Group' },
  { key: 'DPT_NM', label: 'Department' },
  { key: 'CLSS_NM', label: 'Class' },
  { key: 'SUB_CLSS_NM', label: 'Subclass' },
  { key: 'MIN_LM', label: 'Min LM' },
  { key: 'max_sat_lm', label: 'Max LM' },
  { key: 'current_lm', label: 'Current LM' },
  { key: 'optimized_lm', label: 'Optimized LM' },
  { key: 'lm_delta', label: 'Space Change Absolute' },
  { key: 'space_change_precent', label: 'Space Change %' },
  { key: 'current_per_day_metric', label: metric.baseLabel, numeric: true },
  { key: 'new_metric_per_day', label: metric.optLabel, numeric: true },
  { key: 'evaluation_period_new_matric', label: metric.evalPeriodLabel, numeric: true },
  { key: `${metricShortNm}_change_percent`, label: metric.changeLabel },
  { key: 'Final_Action', label: 'Recommendation' },
  { key: 'AVG_OPTN_CNT', label: 'Option Count' },
  { key: 'OPTION_DENSITY', label: 'Option Density' },
  { key: 'optimized_no_of_options', label: 'Optimized Options' },
  { key: 'options_change', label: 'Option Change' },
  { key: 'MNTH_AVG_SOH_AVG', label: 'Quantity' },
  { key: 'DEPTH', label: 'Depth' },
  { key: 'optimized_qty', label: 'Optimized Quantity' },
  { key: 'quantity_change', label: 'Quantity Change' }
]

const RangetableColumns = [
  { key: 'loc_cd', label: 'Store' },
  { key: 'grp_nm', label: 'Group' },
  { key: 'dpt_nm', label: 'Department' },
  { key: 'clss_nm', label: 'Class' },
  { key: 'sub_clss_nm', label: 'Subclass' },
  { key: 'min_lm', label: 'Min LM' },
  { key: 'max_sat_lm', label: 'Max LM' },
  { key: 'range_based_max_lm', label: 'Range Max LM' },
  { key: 'current_lm', label: 'Current LM' },
  { key: 'optimized_lm', label: 'Optimized LM' },
  { key: 'lm_delta', label: 'Space Change Absolute' },
  { key: 'space_change_precent', label: 'Space Change %' },
  { key: `${rangeMetric.baseKeyColoumnData}`, label: rangeMetric.baseLabel, numeric: true },
  { key: 'new_metric', label: rangeMetric.optLabel, numeric: true },
  { key: `${metricShortNm}_change_percent`, label: rangeMetric.changeLabel },
  { key: 'final_action', label: 'Recommendation' },
  { key: 'optimized_no_of_options', label: 'Optimized Options' },
  { key: 'depth', label: 'Depth' },
  { key: 'optimized_qty', label: 'Optimized Quantity' }
  
]

const currentPage = ref(1)
const rowsPerPage = 15
let changedRecords = ref({})
const scrollContainer = ref(null)
let isFetchingNextPage = ref(false);
let loadingOptimizerMessage =  ref('Running Optimizer...')

const totalPages = computed(() =>
  Math.ceil(dataCount.value / rowsPerPage)
)

const filteredData = ref([])
const filteredRangeData = ref([])
const showFiltered = ref(false)
const showRangeFiltered = ref(false)


async function applyFilters() {
  currentPage.value = 1
  loadingOptimizerMessage.value = "Applying filters..."
  isLoading.value = true
  if(runMode.value == 'Optimization'){
    showFiltered.value = true
  await fetchOptimizationData(currentPage.value, rowsPerPage);
  await getUpliftData();
  calculateAggregatedMetrics();
  checkNeedForPagination();
  }
  else{
    showRangeFiltered.value = true
    console.log("showRangeFiltered", showRangeFiltered.value)
    await fetchRangeData(currentPage.value, rowsPerPage);
    await getRangeUpliftData();
    calculateAggregatedMetrics();
    checkNeedForPagination();
  }
}

const paginatedData = computed(() => {
  return showFiltered.value ? filteredData.value : optimizationData.value
})
const paginatedRangeData = computed(() => {
  return showRangeFiltered.value ? filteredRangeData.value : rangeOptimizationData.value
})

const capitalizeWords = (str: string): string => {
  return str.toLowerCase().replace(/\b\w/g, char => char.toUpperCase());
}


const recordChange = (row, key, newValue) => {
  const currentLm = parseFloat(row.current_lm);
  if (key === 'space_change_precent') {
    const percent = parseFloat(newValue);
    if (!isNaN(percent) && !isNaN(currentLm)) {
      const absDelta = (currentLm * Number(percent) / 100 ).toFixed(2);
      // if (Math.round(Number(absDelta)) === Math.round(currentLm)) return;
    row.lm_delta = absDelta
      // Also update changedRecords for lm_delta
      if (!changedRecords.value[row.LOC_CD]) changedRecords.value[row.LOC_CD] = {}
      changedRecords.value[row.LOC_CD][row.SUB_CLSS_NM] = parseFloat(absDelta) + currentLm
      
      return
    }
  }

  if (key === 'lm_delta') {
    const delta = parseFloat(newValue);
    if (!isNaN(delta) && !isNaN(currentLm) && currentLm !== 0) {
      const percent = ((delta / currentLm) * 100).toFixed(2);

      const absDelta = (currentLm * Number(percent) / 100 ).toFixed(2);
      // if (Math.round(Number(absDelta)) === Math.round(currentLm)) return;
      row.space_change_precent = percent;
      row.lm_delta = absDelta

    if (!changedRecords.value[row.LOC_CD]) changedRecords.value[row.LOC_CD] = {};
    changedRecords.value[row.LOC_CD][row.SUB_CLSS_NM] = parseFloat(absDelta) +currentLm;
    return;
    }
  }

  // For other fields
  if (!changedRecords.value[row.LOC_CD]) changedRecords.value[row.LOC_CD] = {};
  changedRecords.value[row.LOC_CD][row.SUB_CLSS_NM] = newValue+ currentLm;
}
const formatValue = (val: any, isNumeric = false, colKey = '') => {
  if (val === null || val === '') return '-';
  if (colKey === 'loc_cd') {
    return String(val);
  }
  
  const textFieldsToCapitalize = ['GRP_NM', 'DPT_NM', 'CLSS_NM', 'SUB_CLSS_NM', 'grp_nm', 'dpt_nm', 'clss_nm', 'sub_clss_nm'];
  if (typeof val === 'string' && textFieldsToCapitalize.includes(colKey)) {
    return capitalizeWords(val);
  }

  if (typeof val === 'number') {
    const noDecimalFields = [ 'Revenue_sum_reference_month', 'Revenue_sum_optimized', 'GMV_sum_reference_month', 'GMV_sum_optimized', 'new_metric', 'optimized_no_of_options', 'DEPTH', 'optimized_qty', 'options_change', 'quantity_change','AVG_OPTN_CNT', 'MNTH_AVG_SOH_AVG'];
    if (noDecimalFields.includes(colKey)) {
      return Math.round(val).toLocaleString('en-US', { maximumFractionDigits: 0 });
    }
    if (isNumeric) return Math.round(val).toLocaleString('en-US', { maximumFractionDigits: 0 });
    return val.toFixed(2);
  }
  if(colKey == 'optimized_qty'){
    return Math.round(val).toLocaleString('en-US', { maximumFractionDigits: 0 });
  }

  return val;
}


const selectedStores = ref([])
const selectedGroups = ref([])
const selectedDepartments = ref([])
const selectedClasses = ref([])
const selectedSubClasses = ref([])
const selectedRecommendation = ref([])
const isDownload = ref(false)
const isRangeDownload = ref(false)
const isResetting = ref(false)
// Reset methods
const resetOnStoreChange = () => {
  selectedGroups.value = []
  selectedDepartments.value = []
  selectedClasses.value = []
  selectedSubClasses.value = []
  selectedRecommendation.value = []
}

const resetOnGroupChange = () => {
  selectedDepartments.value = []
  selectedClasses.value = []
  selectedSubClasses.value = []
  selectedRecommendation.value = []
}

const resetOnDepartmentChange = () => {
  selectedClasses.value = []
  selectedSubClasses.value = []
  selectedRecommendation.value = []
}

const resetOnClassChange = () => {
  selectedSubClasses.value = []
  selectedRecommendation.value = []
}
const clearAllFilters = () => {
  selectedStores.value = []
  selectedGroups.value = []
  selectedDepartments.value = []
  selectedClasses.value = []
  selectedSubClasses.value = []
  selectedRecommendation.value = []
}

const uniqueStores = computed(() => {
  const stores = allStores.value.map(item => ({
    value: item.LOC_CD,
    label: `${item.LOC_CD} - ${item.loc_nm}`
  }))
  return [...new Map(stores.map(item => [item.value, item])).values()]
})

const uniqueGroups = computed(() => {
  let groupsArray = optimizationFilterData.value || [];

  if (Array.isArray(selectedStores.value) && selectedStores.value.length > 0) {
    // Filter groups that belong to selected stores
    groupsArray = groupsArray.filter(item => selectedStores.value.includes(item.LOC_CD));
  }

  // Get unique group names
  const uniqueGroupNames = [...new Set(groupsArray.map(item => item.GRP_NM))];

  // Map to { value, label }
  return uniqueGroupNames.map(group => ({ value: group, label: group }));
});


const uniqueDepartments = computed(() => {
  
  let filtered = optimizationFilterData.value || []

  // Normalize selectedStores.value to an array if it's a string or empty
  const selectedStoresArray = Array.isArray(selectedStores.value)
    ? selectedStores.value
    : selectedStores.value ? [selectedStores.value] : []

  if (selectedStoresArray.length > 0) {
    filtered = filtered.filter(item => selectedStoresArray.includes(item.LOC_CD))
  }

  if (Array.isArray(selectedGroups.value) && selectedGroups.value.length > 0) {
    filtered = filtered.filter(item => selectedGroups.value.includes(item.GRP_NM))
  }

  // Extract unique department names from filtered data
  const departments = [...new Set(filtered.map(item => item.DPT_NM))]

  return departments.map(dept => ({ value: dept, label: dept }))
})


const uniqueClasses = computed(() => {
  let filtered = optimizationFilterData.value
  if (Array.isArray(selectedStores.value) && selectedStores.value.length > 0) {
    filtered = filtered.filter(item => selectedStores.value.includes(item.LOC_CD))
  }
  if (Array.isArray(selectedGroups.value) && selectedGroups.value.length > 0) {
    filtered = filtered.filter(item => selectedGroups.value.includes(item.GRP_NM))
  }
  if (Array.isArray(selectedDepartments.value) && selectedDepartments.value.length > 0) {
    filtered = filtered.filter(item => selectedDepartments.value.includes(item.DPT_NM))
  }
  const classes = [...new Set(filtered.map(item => item.CLSS_NM))]
  return classes.map(cls => ({ value: cls, label: cls }))
})

const uniqueSubClasses = computed(() => {
  let filtered = optimizationFilterData.value
  if (Array.isArray(selectedStores.value) && selectedStores.value.length > 0) {
    filtered = filtered.filter(item => selectedStores.value.includes(item.LOC_CD))
  }
  if (Array.isArray(selectedGroups.value) && selectedGroups.value.length > 0) {
    filtered = filtered.filter(item => selectedGroups.value.includes(item.GRP_NM))
  }
  if (Array.isArray(selectedDepartments.value) && selectedDepartments.value.length > 0) {
    filtered = filtered.filter(item => selectedDepartments.value.includes(item.DPT_NM))
  }
  if (Array.isArray(selectedClasses.value) && selectedClasses.value.length > 0) {
    filtered = filtered.filter(item => selectedClasses.value.includes(item.CLSS_NM))
  }
  const subClasses = [...new Set(filtered.map(item => item.SUB_CLSS_NM))]
  return subClasses.map(subCls => ({ value: subCls, label: capitalizeFirst(subCls) }))
})

function capitalizeFirst(str: string) {
  if (!str) return '';
  return str
    .toLowerCase()
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}


const uniqueRecommendations = computed(() => {
  const recommendations = ['Increase', 'Decrease', 'No Change']
  return recommendations.map(rec => ({ value: rec, label: rec }))
})


function getStickyClass(index) {
  const stickyMap = {
    0: 'left-0 min-w-[60px]',
    1: 'left-[60px] min-w-[75px]',
    2: 'left-[135px] min-w-[90px]',
    3: 'left-[225px] min-w-[140px]',
    4: 'left-[365px] min-w-[150px]'
  };
  // Add `sticky`, `bg-gray-100`, and higher z-index for overlap handling
  return index <= 4 ? ` ${stickyMap[index]} z-30 bg-gray-100` : '';
}

const formattedMetric = computed(() => {
  let performanceMetric = sessionStorage.getItem('performance_metric')
  if (performanceMetric === 'REVENUE') {
    performanceMetric = 'Revenue'
  } else if (performanceMetric === 'GMV') {
    performanceMetric = 'GMV'
  } else {
    performanceMetric = 'Metric'
  }
  return performanceMetric
})


function calculateAggregatedMetrics() {
  const selected = Array.isArray(selectedStores.value)
    ? selectedStores.value
    : (selectedStores.value ? [selectedStores.value] : []);

  const storeIds = selected.length
    ? selected
    : upliftData.value.map(item => item.loc_cd);

  // Aggregated Optimized Metric
  let totalOptimized = 0;
  for (const storeId of storeIds) {
    const value = upliftData.value.find(item => item.loc_cd === storeId)?.evaluation_period_new_matric;
    const num = Number(value ?? 0);
    if (!isNaN(num)) {
      totalOptimized += num;
    }
  }
  aggregatedOptimizedMetric.value = totalOptimized;

  // Aggregated Uplift %
  let sum = 0;
  let count = 0;
  for (const storeId of storeIds) {
    const value = upliftData.value.find(item => item.loc_cd === storeId)?.daily_pct_lift;
    const num = Number(value);
    if (!isNaN(num)) {
      sum += num;
      count++;
    }
  }
  aggregatedUpliftPct.value = count ? sum / count : 0;
}



const completeOptimization = async () => {
  try {
    await axios.post('scenario/completeOptimization/', {
      scenario_id: sessionStorage.getItem('scenario_id'),
    })
    router.push({ name: 'HomePage' })
  } catch (err) {
    console.error('Error completing optimization:', err)
  }
}

function debounce(fn, delay = 200) {
  let timeout;
  return function (...args) {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => fn.apply(this, args), delay);
  };
}


const handleScroll = async () => {
  const el = scrollContainer.value;
  if (!el || isLoading.value) return;

  // Only proceed if vertical scroll happened
  const currentScrollTop = el.scrollTop;
  if (currentScrollTop === lastScrollTop) return;

  lastScrollTop = currentScrollTop;

  const threshold = 100;
  const isNearBottom = el.scrollTop + el.clientHeight >= el.scrollHeight - threshold;

  if (isNearBottom && currentPage.value < totalPages.value) {
    isFetchingNextPage.value = true;
    currentPage.value++;
    await fetchOptimizationData(currentPage.value, rowsPerPage, true);
    isFetchingNextPage.value = false;
  }
};

const handleRangeScroll = async () => {
  const el = scrollContainer.value;
  if (!el || isLoading.value) return;

  // Only proceed if vertical scroll happened
  const currentScrollTop = el.scrollTop;
  if (currentScrollTop === lastScrollTop) return;

  lastScrollTop = currentScrollTop;

  const threshold = 100;
  const isNearBottom = el.scrollTop + el.clientHeight >= el.scrollHeight - threshold;

  if (isNearBottom && currentPage.value < totalPages.value) {
    isFetchingNextPage.value = true;
    currentPage.value++;
    await fetchRangeData(currentPage.value, rowsPerPage, true);
    isFetchingNextPage.value = false;
  }
};


const debouncedScrollHandler = debounce(handleScroll, 200);
const debouncedRangeScrollHandler = debounce(handleRangeScroll, 200);

watch(runMode, async (val) => {
  currentPage.value = 1
  optimizationData.value = []
  if (val === 'Range') {
    let isValid = await getOptimizerRunStatus(sessionStorage.getItem('scenario_id'), 'runRange')
    if (!isValid){
      await runRange('range_optimization')
    }
    await fetchRangeData(currentPage.value, rowsPerPage)
    await getRangeUpliftData();
    calculateAggregatedMetrics();
    checkNeedForPagination();
  } else {
    await fetchOptimizationData(currentPage.value, rowsPerPage)
    await getUpliftData();
    calculateAggregatedMetrics();
    checkNeedForPagination();
  }
})

const saveOptimiserProgress = async (action) => {
  try {
    const lastseenPage = stepsStore.currentStep
    await updateScenarioStatus({
      scenario_id: sessionStorage.getItem('scenario_id'),
      current_page: lastseenPage,
      progress_page: lastseenPage 
    })
    if (action === 'next') {
      stepsStore.goToNextStep()
    } else if (action === 'prev') {
      stepsStore.goToStep(lastseenPage - 1)
    }
  } catch (err) {
    console.error('Error updating scenario status:', err)
  }
}
async function downloadCsv() {
  isDownload.value = true
  const concept = sessionStorage.getItem('concept');
  const territory = sessionStorage.getItem('territory_name');
  const scenario_id = sessionStorage.getItem('scenario_id');

  if (!concept || !territory || !scenario_id) {
    return;
  }

  try {
    // Make POST request to the backend
    const response = await axios.post('scenario/downloadOptimizationSummary/', {
      concept,
      territory,
      scenario_id,
      metricShortNm
    });

    const source = response.data.data;

    if (!source || source.length === 0) {
      return;
    }

    // Build CSV from your `tableColumns` config
    const headers = tableColumns.map(col => col.label);
    const keys = tableColumns.map(col => col.key);

    const csvRows = [
      headers.join(','), // Header row
      ...source.map(row =>
        keys.map(key => {
          const colConfig = tableColumns.find(col => col.key === key);
          const isNumeric = colConfig?.numeric || false;
          const formatted = formatValue(row[key], isNumeric, key);

          const safeVal = typeof formatted === 'string' && /[,\n"]/.test(formatted)
            ? `"${String(formatted).replace(/"/g, '""')}"`
            : formatted;

          return safeVal ?? '';
        }).join(',')
      )
    ];

    // Create and trigger file download
    const csvContent = csvRows.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `optimization_summary_${new Date().toISOString().slice(0, 10)}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Download error:', error);
  }
  finally{
    isDownload.value = false
  }
}

async function downloadRangeCsv() {
  isRangeDownload.value = true
  const concept = sessionStorage.getItem('concept');
  const territory = sessionStorage.getItem('territory_name');
  const scenario_id = sessionStorage.getItem('scenario_id');

  if (!concept || !territory || !scenario_id) {
    isRangeDownload.value = true
    return;
  }
   try {
    await downloadRangeBasedCsv(scenario_id, concept, territory);
  } catch (error) {
    console.error('Download failed', error);
  } finally {
    isRangeDownload.value = false;
  }
}

async function downloadSummaryCsv() {
  isDownload.value = true
  const concept = sessionStorage.getItem('concept');
  const territory = sessionStorage.getItem('territory_name');
  const scenario_id = sessionStorage.getItem('scenario_id');

  if (!concept || !territory || !scenario_id) {
    isDownload.value = true
    return;
  }
   try {
    await downloadSummaryBasedCsv(scenario_id, concept, territory);
  } catch (error) {
    console.error('Download failed', error);
    // Optionally show error to user
  } finally {
    isDownload.value = false;
  }
}

const resetUserChanges = async () => {
  isResetting.value = true
  try {
    // Clear all changed records
    changedRecords.value = {}

    // Re-fetch data from API to get original values
    currentPage.value = 1
    await fetchOptimizationData(currentPage.value, rowsPerPage,false,true)
    await getUpliftData();
    calculateAggregatedMetrics();
    checkNeedForPagination();
    // Show success toast
    toast.success('Space changes have been reset to defaults successfully!')

  } catch (error) {
    console.error('Reset failed:', error)
    toast.error('Failed to reset changes. Please try again.')
  } finally {
    isResetting.value = false
  }
}

</script>

<template>
  <div class="flex flex-col h-full">
    <div v-if="isLoading" class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
          <div class="bg-white rounded-lg p-2 flex items-center justify-center">
            <div class="tw-loader"></div> 
            <div class="mr-3">{{ loadingOptimizerMessage }}</div>
          </div>
    </div>
    <div class="flex-1 overflow-auto p-4 sm:p-6 lg:p-8 ">
      <div class="">
        <div class="bg-white rounded-lg shadow-sm p-2 mb-2">
          <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-4">
            <div>
              <label class="ml-2 text-sm font-medium text-gray-700">Store</label>
              <DynamicFilter v-model="selectedStores" :multiselect="true" label="Store" placeholder="Select Stores"
                :options="uniqueStores" :searchable="(uniqueStores?.length || 0) > 10" variant="secondary" size="sm"
                @change="resetOnStoreChange()" />
            </div>
            <div>
              <label class="ml-2 text-sm font-medium text-gray-700">Group</label>
              <DynamicFilter v-model="selectedGroups" :multiselect="true" label="Group" placeholder="Select Group"
                :options="uniqueGroups" :searchable="(uniqueGroups?.length || 0) > 10" variant="secondary" size="sm"
                @change="resetOnGroupChange()" />
            </div>
            <div>
              <label class="ml-2 text-sm font-medium text-gray-700">Department</label>
              <DynamicFilter v-model="selectedDepartments" :multiselect="true" label="Department"
                placeholder="Select Department" :options="uniqueDepartments"
                :searchable="(uniqueDepartments?.length || 0) > 10" variant="secondary" size="sm"
                @change="resetOnDepartmentChange()" />
            </div>
            <div>
              <label class="ml-2 text-sm font-medium text-gray-700">Class</label>
              <DynamicFilter v-model="selectedClasses" :multiselect="true" label="Class" placeholder="Select Class"
                :options="uniqueClasses" :searchable="(uniqueClasses?.length || 0) > 10" variant="secondary" size="sm"
                @change="resetOnClassChange()" />
            </div>
            <div>
              <label class="ml-2 text-sm font-medium text-gray-700">Sub Class</label>
              <DynamicFilter v-model="selectedSubClasses" :multiselect="true" label="Sub Classes"
                placeholder="Select Sub Class" :options="uniqueSubClasses"
                :searchable="(uniqueSubClasses?.length || 0) > 10" variant="secondary" size="sm" class="w-full" />
            </div>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-2">
            <!-- Sub Classes -->
            

            <!-- Recommendation -->
            <div>
              <label class="ml-2 text-sm font-medium text-gray-700">Recommendation</label>
              <DynamicFilter v-model="selectedRecommendation" :multiselect="true" label="Recommendation"
                placeholder="Recommendation" :options="uniqueRecommendations"
                :searchable="(uniqueRecommendations?.length || 0) > 10" variant="secondary" size="sm" class="w-full" />
            </div>

            <!-- Buttons (span 2 columns) -->
            <div class="col-span-4 flex items-end justify-between w-full ">
              <div class="flex gap-3">
              <button @click="clearAllFilters"
                class="px-4 text-center flex items-center justify-center h-10 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-secondary">
                Clear All
              </button>
              <button @click="applyFilters()"
                class="px-2 text-center flex items-center justify-center h-10 text-sm font-medium border border-transparent rounded-md focus:outline-none focus:ring-2 bg-tertiary hover:bg-tertiary text-tx-primary focus:ring-secondary">
                Apply Filters
              </button>
              <button @click="showProductivityModal = true"
                class="px-2 text-center flex items-center justify-center h-10 text-sm font-medium border border-secondary text-tertiary rounded-md focus:outline-none focus:ring-2 hover:bg-green-50 focus:ring-secondary">
                Saturation Curves
              </button>
              <button v-if="Object.keys(changedRecords).length > 0" @click="reRunOptimizer('optimization')"
                class="whitespace-nowrap px-2 text-center flex items-center justify-center h-10 text-sm font-medium text-tx-primary bg-tertiary border border-transparent rounded-md hover:bg-tertiary focus:outline-none focus:ring-2 focus:ring-blue-500">
                Re-Run Optimizer
              </button>
              <button v-if="runMode == 'Optimization'" @click="downloadCsv()"
              class="px-4 py-2 h-10 text-sm font-medium border border-transparent rounded-md focus:outline-none focus:ring-2 bg-tertiary hover:bg-tertiary text-tx-primary focus:ring-secondary flex items-center gap-2">
              <div v-if="isDownload" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <Download v-else class="h-4 w-4" />
            </button>
            <button v-if="runMode == 'Range'" @click="downloadSummaryCsv()"
              class="px-4 py-2 h-10 text-sm font-medium border border-transparent rounded-md focus:outline-none focus:ring-2 bg-tertiary hover:bg-tertiary text-tx-primary focus:ring-secondary flex items-center gap-2">
              <div v-if="isDownload" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <Download v-else class="h-4 w-4" />
            </button>
      <button v-if="runMode === 'Range'" @click="downloadRangeCsv()"
  class="px-4 py-2 h-10 text-sm font-medium border border-transparent rounded-md focus:outline-none focus:ring-2 bg-tertiary hover:bg-tertiary text-tx-primary focus:ring-secondary flex items-center gap-2">
  <div v-if="isRangeDownload" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
  <Download v-else class="h-4 w-4" /> Range
</button>
            </div>
            <div class="flex justify-end items-center">
    <div v-if="showRangeOptimizerButtons" class="relative w-[200px] h-10 flex rounded-full border border-secondary overflow-hidden">
    <button
      :class="[
        'flex-1 text-sm font-medium transition-all duration-300',
        runMode === 'Optimization'
          ? 'bg-tertiary text-white'
          : 'bg-white text-secondary'
      ]"
      @click="runMode = 'Optimization'"
    >
      Space
    </button>
    <button
      :class="[
        'flex-1 text-sm font-medium transition-all duration-300',
        runMode === 'Range'
          ? 'bg-tertiary text-white'
          : 'bg-white text-secondary'
      ]"
      @click="runMode = 'Range'"
    >
      Range
    </button>
  </div>
  </div>
            </div>
          </div>

        </div>
      </div>
      <div class="p-2 max-w-7xl mx-auto">
        <!-- Search -->
        <div class="mb-2 flex justify-end items-center gap-4">
          <div class="flex items-center">
            <h4 class="font-bold flex items-center gap-2 text-sm">
              Optimized <span>{{ formattedMetric }}</span>
              <div class="relative group">
                <button type="button"
                  class="text-gray-400 hover:text-gray-700 focus:outline-none font-bold border border-gray-300 rounded-full w-3 h-3 flex items-center justify-center text-[0.5rem] bg-white"
                  tabindex="0">
                  i
                </button>
                <div
                  class="absolute left-1/2 -translate-x-1/2 mt-2 w-auto max-w-md rounded-lg bg-gray-700 text-white text-[9px] leading-snug px-3 py-2 shadow-lg z-50 opacity-0 group-hover:opacity-100 group-focus-within:opacity-100 pointer-events-none group-hover:pointer-events-auto group-focus-within:pointer-events-auto transition-opacity whitespace-nowrap"
                  style="pointer-events: none;">
                  <div> 
                    <span>Optimized for the evaluation days</span>
                  </div>
                </div>
              </div>
              :
            </h4>
            <span class="bg-green-100 text-green-600 font-semibold px-2 rounded ml-2">
              {{ Number(aggregatedOptimizedMetric).toLocaleString('en-US', { maximumFractionDigits: 0 }) || 0 }} <span
                class="text-xs">AED</span>
            </span>
          </div>
          <div class="flex items-center">
            <h3 class="font-bold text-sm flex items-center gap-2">
              <span>{{ formattedMetric }}</span> Uplift:
              <div class="relative group">
                <button type="button"
                  class="text-gray-400 hover:text-gray-700 focus:outline-none font-bold border border-gray-300 rounded-full w-3 h-3 flex items-center justify-center text-[0.5rem] bg-white"
                  tabindex="0">
                  i
                </button>
                <div
                  class="absolute left-1/2 -translate-x-1/2 mt-2 w-auto max-w-md rounded-lg bg-gray-700 text-white text-[9px] leading-snug px-3 py-2 shadow-lg z-50 opacity-0 group-hover:opacity-100 group-focus-within:opacity-100 pointer-events-none group-hover:pointer-events-auto group-focus-within:pointer-events-auto transition-opacity whitespace-nowrap"
                  style="pointer-events: none;">
                  <div> 
                    <span>Uplift based on reference period</span>
                  </div>
                </div>
              </div>
            </h3>
            <span class="bg-green-100 text-green-600 font-semibold px-2 rounded ml-2">
              {{ aggregatedUpliftPct.toFixed(2) }}%
            </span>
          </div>
          <div v-if="runMode === 'Optimization'" class="flex items-center">
            <div class="relative inline-flex items-center group">
              <!-- Reset Button -->
              <button
                class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-100 flex items-center gap-2
                       disabled:bg-gray-200 disabled:text-gray-400 disabled:cursor-not-allowed"
                @click="resetUserChanges"
                :disabled="isResetting || Object.keys(changedRecords).length === 0"
              >
                <div v-if="isResetting" class="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"></div>
                <RotateCcw v-else class="w-4 h-5" />
              </button>

              <!-- Tooltip -->
              <div
                class="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 z-50
                       px-4 py-2 bg-slate-800 text-white text-sm rounded-md shadow-xl
                       opacity-0 pointer-events-none transition-all duration-300 ease-out
                       group-hover:opacity-100 group-hover:pointer-events-auto
                       after:content-[''] after:absolute after:top-full after:left-1/2
                       after:-translate-x-1/2 after:border-4 after:border-transparent
                       after:border-t-slate-800"
              >
                <div class="whitespace-nowrap font-medium">Reset to Default</div>
              </div>
            </div>
          </div>
          <!-- <div>
            <span class="text-sm text-gray-500">Showing {{ optimizationData.length }} records</span>
          </div> -->
        </div>

        <!-- Table -->
        <div v-if="runMode === 'Optimization'" class="max-h-[70vh] overflow-x-auto overflow-y-auto bg-white shadow rounded" @scroll="debouncedScrollHandler" ref="scrollContainer">
          <table class="table-auto border-separate border-spacing-0 border-[0.5px] border-gray-300 min-w-full text-xs">
            <thead>
              <tr class="bg-primary text-center text-[13px] font-semibold text-gray-700 whitespace-nowrap">
                <th
                v-for="(col, index) in tableColumns"
                :key="col.key"
                :class="[
    'px-2 py-1 border-b border-r last:border-r-0 text-[13px] font-semibold text-gray-700 whitespace-nowrap text-center',
    'sticky top-0 bg-primary',
    index <= 4 ? getStickyClass(index) : ''
  ]" 
              >
                <!-- {{ col.label }} -->
                  <template v-if="col.key === 'lm_delta'">
  <div class="flex items-center justify-center gap-1 relative group">
    {{ col.label }}
    <div class="relative inline-block group">
  <!-- The "i" icon -->
  <span
    class="text-gray-400 hover:text-gray-700 focus:outline-none font-bold border border-gray-300 rounded-full w-3 h-3 flex items-center justify-center text-[0.5rem] bg-white"
    aria-label="Information"
  >
    i
  </span>

  <!-- Tooltip -->
<div
  class="absolute left-1/2 -translate-x-1/2 mt-2 w-auto max-w-lg rounded-lg bg-gray-700 text-white text-[9px] leading-none px-3 py-2 shadow-lg z-50
         opacity-0 pointer-events-none transition-opacity group-hover:opacity-100 group-hover:pointer-events-auto whitespace-nowrap"
>
  <div>
    Current LM = Space Change Absolute + Current LM
  </div>
</div>
</div>

  </div>
</template>
<template v-else>
  {{ col.label }}
</template>

              </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="row in paginatedData" :key="row.LOC_CD + '-' + row.SUB_CLSS_NM"
                class="hover:bg-gray-50 transition">
                <td v-for="(col, index) in tableColumns"
  :key="col.key"
  :class="[
  'px-2 py-1 border-b border-r last:border-r-0 text-[13px] text-center whitespace-nowrap',
index === 0 ? 'sticky left-0 z-10 bg-white min-w-[60px]' :
index === 1 ? 'sticky left-[60px] z-10 bg-white min-w-[75px]' :
index === 2 ? 'sticky left-[135px] z-10 bg-white min-w-[90px]' :
index === 3 ? 'sticky left-[225px] z-10 bg-white min-w-[140px]' :
index === 4 ? 'sticky left-[365px] z-10 bg-white min-w-[150px]' :
  ''
]">
                  <!-- Editable fields -->
                  <template v-if="col.key === 'lm_delta' || col.key === 'space_change_precent'">
                    <template v-if="isEditing(row, col.key)">
                      <button @click="saveEdit(row, col.key)" class="text-secondary hover:text-green-800 disabled:text-gray-300" :disabled="isDisabled" title="Save">
                        ✔
                      </button>
                      <input type="number" step="1" v-model.number="row[col.key]"
                        class="w-24 px-2 py-1 border rounded-md text-[13px] focus:outline-none focus:ring-2 focus:ring-blue-500 mr-1" />
                    </template>
                    <template v-else>
                      <div class="flex items-center">
                        <button @click="startEdit(row, col.key)" class="ml-2 mr-1 text-blue-600 hover:text-blue-800 disabled:text-gray-300" :disabled="isDisabled"
                          title="Edit">
                          ✎
                        </button>
                        <span>{{ formatValue(row[col.key],false ,col.key) }}</span>
                      </div>
                    </template>
                  </template>
                  <template v-else-if="col.key === `${metricShortNm}_change_percent`">
                    <span>
                      {{ calcMetricChangePercent(row) }}%
                    </span>
                  </template>
                  <template v-else-if="col.key === 'Final_Action'">
                    <span :class="recommendationClass(row.Final_Action)">
                      {{ capitalizeRecommendation(row.Final_Action) }}
                    </span>
                  </template>
                  <template v-else-if="col.key === 'options_change'">
                    <span>
                      {{ calcOptionsChange(row) }}
                    </span>
                  </template>
                  <template v-else-if="col.key === 'quantity_change'">
                    <span>
                      {{ calcQuantityChange(row) }}
                    </span>
                  </template>
                  <!-- Normal fields -->
                  <template v-else>
                    {{ formatValue(row[col.key], col.numeric, col.key) }}
                  </template>
                </td>
              </tr>
            </tbody>
          </table>
          <div v-if="isFetchingNextPage" class="flex items-center justify-center space-x-2 text-gray-500 py-4">
                <Loader class="animate-spin w-4 h-4 text-secondary" />
            </div>
        </div>


        <div v-if="runMode === 'Range'" class="max-h-[70vh] overflow-x-auto overflow-y-auto bg-white shadow rounded" @scroll="debouncedRangeScrollHandler" ref="scrollContainer">
          <table class="table-auto border-separate border-spacing-0 border-[0.5px] border-gray-300 min-w-full text-xs">
            <thead>
              <tr class="bg-primary text-center text-[13px] font-semibold text-gray-700 whitespace-nowrap">
                <th
                v-for="(col, index) in RangetableColumns"
                :key="col.key"
                :class="[
    'px-2 py-1 border-b border-r last:border-r-0 text-[13px] font-semibold text-gray-700 whitespace-nowrap text-center',
    'sticky top-0 bg-primary',
    index <= 4 ? getStickyClass(index) : ''
  ]" 
              >
                <!-- {{ col.label }} -->
                  <template v-if="col.key === 'lm_delta'">
  <div class="flex items-center justify-center gap-1 relative group">
    {{ col.label }}
    <div class="relative inline-block group">
  <!-- The "i" icon -->
  <span
    class="text-gray-400 hover:text-gray-700 focus:outline-none font-bold border border-gray-300 rounded-full w-3 h-3 flex items-center justify-center text-[0.5rem] bg-white"
    aria-label="Information"
  >
    i
  </span>

  <!-- Tooltip -->
<div
  class="absolute left-1/2 -translate-x-1/2 mt-2 w-auto max-w-lg rounded-lg bg-gray-700 text-white text-[9px] leading-none px-3 py-2 shadow-lg z-50
         opacity-0 pointer-events-none transition-opacity group-hover:opacity-100 group-hover:pointer-events-auto whitespace-nowrap"
>
  <div>
    Current LM = Space Change Absolute + Current LM
  </div>
</div>
</div>

  </div>
</template>
<template v-else>
  {{ col.label }}
</template>

              </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="row in paginatedRangeData" :key="row.loc_cd + '-' + row.sub_clss_nm"
                class="hover:bg-gray-50 transition">
                <td v-for="(col, index) in RangetableColumns"
  :key="col.key"
  :class="[
  'px-2 py-1 border-b border-r last:border-r-0 text-[13px] text-center whitespace-nowrap',
index === 0 ? 'sticky left-0 z-10 bg-white min-w-[60px]' :
index === 1 ? 'sticky left-[60px] z-10 bg-white min-w-[75px]' :
index === 2 ? 'sticky left-[135px] z-10 bg-white min-w-[90px]' :
index === 3 ? 'sticky left-[225px] z-10 bg-white min-w-[140px]' :
index === 4 ? 'sticky left-[365px] z-10 bg-white min-w-[150px]' :
  ''
]">
                  <!-- Editable fields -->
                  <template v-if="col.key === 'lm_delta' || col.key === 'space_change_precent'">
                    <span>{{ formatValue(row[col.key], false, col.key) }}</span>
                </template>
                  <template v-else-if="col.key === `${metricShortNm}_change_percent`">
                    <span>
                      {{ calcMetricChangePercent(row) }}%
                    </span>
                  </template>
                  <template v-else-if="col.key === 'final_action'">
                    <span :class="recommendationClass(row.final_action)">
                      {{ capitalizeRecommendation(row.final_action) }}
                    </span>
                  </template>
                  <!-- Normal fields -->
                  <template v-else>
                    {{ formatValue(row[col.key], col.numeric, col.key) }}
                  </template>
                </td>
              </tr>
            </tbody>
          </table>
          <div v-if="isFetchingNextPage" class="flex items-center justify-center space-x-2 text-gray-500 py-4">
                <Loader class="animate-spin w-4 h-4 text-secondary" />
            </div>
        </div>



        <div class="flex justify-between mt-4 ml-8" v-if="!isFetchingNextPage">
          <button @click="saveOptimiserProgress('prev')"
            class="border border-gray-300 text-black px-4 font-semibold py-2  mb-8 rounded hover:bg-gray-100 ">
            ← Performance
          </button>
          <button @click="showAlert = true"
            class="bg-tertiary hover:bg-tertiary text-tx-primary font-semibold mr-8 mb-8 cursor-pointer hover:bg-tertiary px-6 py-2 rounded mr-2">
            Complete
          </button>
        </div>

        <!-- Debug: Show changed records -->
        <!-- <div class="mt-6 bg-gray-50 p-4 rounded-lg">
          <h3 class="font-semibold text-gray-700 mb-2">Changed Records</h3>
          <pre class="text-[13px] text-gray-600">{{ changedRecords }}</pre>
        </div> -->
      </div>
    </div>
    <AlertPage
      :message="alertMessage"
      :header="headerMessage"
      :visible="showAlert"
      @ok="handleOk"
      @cancel="handleCancel"
    />
    <!-- Productivity Charts Modal -->
    <div v-if="showProductivityModal"
      class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
      <div class="bg-white rounded-lg shadow-lg w-[95vw] h-[95vh] max-w-[1400px] overflow-hidden flex flex-col">
        <div class="flex items-center justify-between border-b px-4 py-2">
          <h3 class="text-lg font-semibold">Saturation Analysis</h3>
          <button @click="showProductivityModal = false" class="text-gray-600 hover:text-gray-900">✕</button>
        </div>
        <div class="flex-1 overflow-auto px-2">
          <ProductivityChartsDashboard :key="showProductivityModal ? 'prod-open' : 'prod-closed'" />
        </div>
      </div>
    </div>
  </div>
</template>